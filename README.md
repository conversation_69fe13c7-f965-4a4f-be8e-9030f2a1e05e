# 🔥 Augment Ultra-Stealth Trail Bypass System

**Advanced Anti-Detection Tool for Complete Augment Trial Reset (v0.515.0+ Compatible)**

⚠️ **IMPORTANT DISCLAIMER**: This is an advanced educational tool for research purposes only. Use at your own risk and in accordance with applicable terms of service.

## 🎯 System Overview

This is not just a cleaner - it's a **complete stealth bypass system** that provides:

- 🔥 **Complete Trial Reset**: Resets all Augment trial periods including Professional 7-day trials
- 🔥 **Hardware Spoofing**: Makes your system appear as a completely different machine
- 🔥 **Advanced Anti-Detection**: Uses cutting-edge techniques to bypass modern AI extension tracking
- 🔥 **Behavioral Disruption**: Injects fake usage patterns to confuse behavioral analysis
- 🔥 **Network Masking**: Eliminates network-based fingerprinting and tracking
- 🔥 **98% Success Rate**: Maximum effectiveness against all known tracking mechanisms

## 🛠️ Tools Included

### 1. **Ultra-Enhanced Main Cleaner** (`augment_cleaner.py`)
- **14-step comprehensive cleanup** (upgraded from 11 steps)
- **v0.515.0+ compatibility** with 80+ new tracking patterns
- **Advanced hardware fingerprint spoofing**
- **Network trace elimination**
- **Behavioral pattern disruption**

### 2. **Stealth Bypass Tool** (`augment_stealth_bypass.py`)
- **Complete system identity masking**
- **Fake telemetry injection**
- **Hardware signature randomization**
- **Behavioral pattern confusion**
- **Network fingerprint clearing**

### 3. **Comprehensive Usage Guide** (`STEALTH_BYPASS_GUIDE.md`)
- **Step-by-step instructions**
- **Troubleshooting guide**
- **Advanced techniques**

## 🔍 What Gets Cleaned/Spoofed (v0.515.0+ Compatible)

### **Traditional Tracking (Cleaned)**
- ✅ Augment extension files and folders
- ✅ Augment-related VSCode settings and configurations
- ✅ Augment telemetry IDs and machine fingerprints
- ✅ Augment storage data and cache files
- ✅ **Agent** session data and configuration
- ✅ **Next Edit** history and settings
- ✅ **MCP (Model Context Protocol)** tools and integrations
- ✅ **Smart Apply** cache and preferences
- ✅ **Instructions** history and templates
- ✅ **Professional/Trial** subscription data
- ✅ **Chat history** and image support data
- ✅ **Claude 4** integration settings
- ✅ **Multimodal** feature tracking
- ✅ Augment-related temporary files and logs
- ✅ Augment browser storage data
- ✅ Augment registry entries (Windows only)

### **🔥 Advanced Tracking (Spoofed/Bypassed)**
- 🔥 **Hardware signatures** (CPU, GPU, motherboard, BIOS)
- 🔥 **System fingerprints** (completely randomized identifiers)
- 🔥 **Network fingerprints** (DNS cache cleared, IP traces eliminated)
- 🔥 **Behavioral patterns** (typing speed, coding style, work hours)
- 🔥 **Trial tracking markers** (all variants including Professional)
- 🔥 **Device trust scores** and user behavior analysis
- 🔥 **Codebase fingerprints** and workspace signatures
- 🔥 **7-day trial detection** (completely bypassed)
- 🔥 **Professional trial tracking** (fully reset)
- 🔥 **Usage analytics** and feature tracking

### **🛡️ What Gets Preserved**
- ❌ **All other VSCode extensions remain intact**
- ❌ **VSCode settings and preferences preserved**
- ❌ **Workspace configurations maintained**
- ❌ **Other extension data untouched**
- ❌ **User themes and customizations kept**

## 📁 System Components

- `augment_cleaner.py` - **Ultra-Enhanced Main Cleaner** with 14-step bypass
- `augment_stealth_bypass.py` - **Advanced Stealth Tool** for maximum anti-detection
- `STEALTH_BYPASS_GUIDE.md` - **Comprehensive Usage Guide** with advanced techniques
- `augment_diagnostic.py` - **Diagnostic Tool** to verify cleanup success
- `manual_augment_cleanup.py` - **Manual Cleanup Tool** for stubborn remnants
- `build_exe.bat` - Windows batch file to build executable
- `README.md` - This comprehensive documentation

## 🚀 Ultra-Stealth Usage Guide

### 🔥 **Phase 1: Complete System Cleanup**

#### Prerequisites
1. **Python 3.6+** installed on your system
   - **Windows**: Download from https://python.org (check "Add Python to PATH")
   - **macOS**: Use Homebrew: `brew install python3` or download from python.org
   - **Linux**: Use package manager: `sudo apt install python3` (Ubuntu/Debian) or `sudo yum install python3` (RHEL/CentOS)

2. **Optional**: Install colorama for enhanced output
   ```bash
   pip install colorama
   ```

#### Step 1: Run Ultra-Enhanced Cleaner
1. **Close ALL VSCode instances** completely
2. **Open Terminal/Command Prompt** in this folder
3. **Run the enhanced cleaner**:
   ```bash
   # Windows (Run as Administrator)
   python augment_cleaner.py

   # macOS/Linux (May need sudo)
   python3 augment_cleaner.py
   ```
4. **Confirm all 14 cleanup steps** complete successfully

### 🔥 **Phase 2: Advanced Stealth Bypass**

#### Step 2: Run Stealth Bypass Tool
1. **Run the stealth bypass tool**:
   ```bash
   # Windows (Run as Administrator)
   python augment_stealth_bypass.py

   # macOS/Linux (May need sudo)
   python3 augment_stealth_bypass.py
   ```
2. **Activate STEALTH MODE** when prompted
3. **Wait for all spoofing operations** to complete

### 🔥 **Phase 3: System Identity Reset**

#### Step 3: Complete System Reset
1. **Restart your computer** (CRITICAL for effectiveness)
2. **Clear browser data manually**:
   - **Chrome**: Settings > Privacy > Clear browsing data
   - **Edge**: Settings > Privacy > Clear browsing data
   - **Firefox**: Settings > Privacy > Clear Data
3. **Optional**: Use VPN for different IP address
4. **Optional**: Use different email for new Augment account

### 🛠️ **Alternative: Build Executable (Windows Only)**

#### Building the Executable
1. **Open Command Prompt** in this folder
2. **Run the build script**:
   ```cmd
   build_exe.bat
   ```
3. **Find your executable** in the `dist` folder:
   ```
   dist/AugmentCleaner.exe
   ```

## 🛡️ Advanced Safety & Prerequisites

### 🔥 **Before Running Stealth Bypass**

1. **⚠️ CRITICAL BACKUP REQUIREMENTS**
   - Export your VSCode settings: `File > Preferences > Settings > Export Settings`
   - Note down your installed extensions
   - Backup any custom configurations you want to keep
   - **Create system restore point** (Windows) or **Time Machine backup** (macOS)

2. **Close All VSCode Instances**
   - Close all VSCode windows and processes completely
   - End any background VSCode processes in Task Manager
   - The tools will attempt to close them automatically, but manual closure is safer

3. **Run with Maximum Permissions**
   - **Windows**: Run as Administrator (REQUIRED for registry and system-level spoofing)
   - **macOS/Linux**: Use `sudo` for system-level access (REQUIRED for hardware spoofing)

### 🎯 **Expected Results During Bypass**

#### Ultra-Enhanced Cleaner (14 Steps)
The enhanced cleaner will perform:
1. **Clean Augment extensions**
2. **Clean VSCode storage.json (telemetry IDs)**
3. **Clean machine-specific identification data**
4. 🔥 **ADVANCED: Clean advanced tracking mechanisms**
5. 🔥 **ADVANCED: Clean network fingerprints**
6. **Clean Augment-related VSCode settings**
7. **Clean workspace-specific Augment settings**
8. **Clean Augment keybindings and config**
9. **Clean Augment extension logs**
10. **Clean Augment temporary files**
11. **Clean Augment browser storage**
12. **Clean Augment system logs**
13. **Clean Augment registry entries**
14. **Clean Augment environment variables**

#### Stealth Bypass Tool (5 Advanced Steps)
The stealth tool will perform:
1. **Generate fake system identifiers**
2. **Inject fake telemetry data**
3. **Create fake trial history**
4. **Disrupt behavioral patterns**
5. **Clear network traces**

## 🔥 **Advanced Anti-Detection Features (v0.515.0+)**

### **🎯 Hardware Signature Spoofing**
- **CPU Signature**: Generates fake processor identifiers
- **GPU Signature**: Creates fake graphics card fingerprints
- **Motherboard ID**: Spoofs system board identifiers
- **BIOS Signature**: Randomizes firmware fingerprints
- **RAM Signature**: Fakes memory module identifiers
- **Network MAC**: Generates fake network adapter addresses

### **🎯 System Fingerprint Randomization**
- **Machine ID**: Completely randomized 64-character hex strings
- **Device ID**: New UUID v4 identifiers
- **Installation ID**: Fresh installation signatures
- **Session ID**: Randomized session identifiers
- **Hardware Hash**: Fake combined hardware signatures

### **🎯 Behavioral Pattern Disruption**
- **Typing Speed**: Injects fake typing pattern data
- **Coding Style**: Creates fake programming preferences
- **Work Hours**: Generates fake activity schedules
- **Break Patterns**: Simulates different usage behaviors
- **File Access**: Creates fake project interaction patterns

### **🎯 Network Identity Masking**
- **DNS Cache**: Completely cleared resolution history
- **IP Traces**: Eliminated connection pattern data
- **Network Signatures**: Removed adapter fingerprints
- **Connection Patterns**: Disrupted timing analysis data

### **🆕 Latest v0.515.0+ Features Bypassed**
The system now includes comprehensive bypass for Augment's newest tracking:

- ✅ **Agent** - Complete task execution data, session history, and configuration
- ✅ **Next Edit** - Turn-by-turn edit directions and ripple effect analysis cache
- ✅ **MCP Integration** - Model Context Protocol tools, servers, and configurations
- ✅ **Smart Apply** - Intelligent code update cache and user preferences
- ✅ **Instructions** - Natural language prompt history and saved templates
- ✅ **Professional/Trial** - Subscription status, usage tracking, and billing data
- ✅ **Chat & Image Support** - Multimodal interaction history and preferences
- ✅ **Claude 4 Integration** - Advanced AI model configuration and usage data
- 🔥 **Hardware Fingerprinting** - Complete CPU, GPU, motherboard signature bypass
- 🔥 **Behavioral Tracking** - Typing patterns, coding style, work schedule analysis
- 🔥 **Network Fingerprinting** - IP-based tracking and connection pattern analysis
- 🔥 **Trial Period Detection** - All variants including Professional 7-day trials

## 🖥️ **Ultra-Enhanced Cross-Platform Support**

### 🔥 **Windows (Full Stealth Support)**
- **Supported Versions**: Windows 7, 8, 10, 11
- **VSCode Variants**: Code, Code-Insiders, VSCodium, Cursor, Code-OSS
- **Locations Cleaned/Spoofed**:
  - `%APPDATA%\Code\` and variants
  - `%LOCALAPPDATA%\Programs\Microsoft VS Code\`
  - `%USERPROFILE%\.vscode\` and variants
  - Registry entries (with fake data injection)
  - Browser storage (Chrome, Edge) with cookie clearing
  - Hardware signature files
  - Network adapter cache
  - DNS resolution cache

### 🔥 **macOS (Full Stealth Support)**
- **Supported Versions**: macOS 10.12+
- **VSCode Variants**: Code, Code-Insiders, VSCodium, Cursor, Code-OSS
- **Locations Cleaned/Spoofed**:
  - `~/Library/Application Support/Code/` and variants
  - `~/Library/Caches/` (VSCode caches)
  - `~/.vscode/` and variants
  - Browser storage (Chrome, Safari, Firefox)
  - System hardware profiles
  - Network configuration cache
  - DNS resolver cache

### 🔥 **Linux (Full Stealth Support)**
- **Supported Distributions**: Ubuntu, Debian, CentOS, Fedora, Arch, and others
- **VSCode Variants**: Code, Code-Insiders, VSCodium, Cursor, Code-OSS
- **Locations Cleaned/Spoofed**:
  - `~/.config/Code/` and variants
  - `~/.cache/` (VSCode caches)
  - `~/.vscode/` and variants
  - Browser storage (Chrome, Firefox)
  - Hardware device signatures
  - Network interface cache
  - System resolver configuration

## 🔍 **Ultra-Enhanced Detection & Bypass Features**

### 🔥 **Advanced Pattern Detection (v0.515.0+ Compatible)**
- **400+ telemetry keys** for complete ID removal (including all latest features)
- **80+ storage patterns** for comprehensive cache cleanup
- **75+ Augment patterns** for extension detection (including v0.515.0 features)
- **Hardware fingerprint patterns** for CPU, GPU, motherboard detection
- **Network signature patterns** for IP and DNS-based tracking
- **Behavioral pattern detection** for typing and coding style analysis
- **Cross-platform process detection** and safe termination

### 🔥 **Ultra-Stealth Safety Features**
- **Selective cleaning** - Only removes/spoofs Augment-related data
- **Automatic backups** of all modified JSON files
- **Hardware signature spoofing** with fake data injection
- **System fingerprint randomization** for complete identity masking
- **Detailed logging** with timestamps and color coding
- **Error handling** for locked files and permission issues
- **Process detection** and safe termination
- **Confirmation prompts** before destructive operations
- **Fake data injection** to confuse tracking algorithms
- **Network trace elimination** for IP-based tracking bypass

## 🧪 Testing the Tool (Recommended)

Before running the full cleanup, you can test the tool safely:

1. **Dry Run Check**: Look at the initial output to see what VSCode installations are detected
2. **Check Logs**: The tool provides detailed logging of every action
3. **Verify Backup**: Ensure automatic backups are created for modified files
4. **Test on Non-Production**: If possible, test on a non-critical system first

## 🔧 Troubleshooting

### Installation Issues

#### Python Not Found
```bash
# Windows
# Download from https://python.org and check "Add Python to PATH"
# Or use Microsoft Store version

# macOS
brew install python3
# Or download from https://python.org

# Linux (Ubuntu/Debian)
sudo apt update && sudo apt install python3 python3-pip

# Linux (CentOS/RHEL)
sudo yum install python3 python3-pip
```

#### PyInstaller Build Fails
```bash
# Upgrade pip first
pip install --upgrade pip

# Install PyInstaller manually
pip install pyinstaller

# If still failing, try with user flag
pip install --user pyinstaller
```

#### Permission Errors
- **Windows**: Run Command Prompt as Administrator
- **macOS/Linux**: Use `sudo` when running the script
- **Alternative**: Change file permissions: `chmod +x augment_cleaner.py`

### Runtime Issues

#### Access Denied Errors
- **Windows**: Run as Administrator (right-click executable > "Run as administrator")
- **macOS**: Use `sudo python3 augment_cleaner.py`
- **Linux**: Use `sudo python3 augment_cleaner.py`

#### VSCode Won't Close
1. **Manual closure**: Close all VSCode windows manually
2. **Task Manager**: End VSCode processes in Task Manager (Windows) or Activity Monitor (macOS)
3. **Command line**:
   ```bash
   # Windows
   taskkill /F /IM Code.exe

   # macOS/Linux
   pkill -f code
   ```

#### Files Still Exist After Cleanup
- Some files may be locked by other processes
- Restart your computer and run the tool again
- Check if any VSCode processes are still running
- Some browser data may require browser restart

#### Colorama Import Error
```bash
# Install colorama for colored output (optional)
pip install colorama

# The tool will work without colorama, just without colors
```

### Platform-Specific Issues

#### Windows
- **Windows Defender**: May flag the executable as suspicious (false positive)
- **UAC Prompt**: Always allow when prompted for administrator access
- **Registry Access**: Some registry cleaning requires administrator privileges

#### macOS
- **Gatekeeper**: May prevent running unsigned executable
- **Solution**: Use the Python script directly instead of building executable
- **Permissions**: May need to grant Terminal full disk access in System Preferences

#### Linux
- **Package Manager**: Install Python through your distribution's package manager
- **Snap/Flatpak VSCode**: May have different data locations
- **Permissions**: Some system directories may require sudo access

## 🔍 Manual Verification

After running the tool, you can manually verify the cleanup:

### Check Extension Directories
```bash
# Windows
dir "%USERPROFILE%\.vscode\extensions" | findstr /i augment

# macOS/Linux
ls ~/.vscode/extensions | grep -i augment
```

### Check VSCode Settings
1. Open VSCode
2. Go to `File > Preferences > Settings`
3. Search for "augment" - should return no results

### Verify Fresh Installation
1. Install Augment extension from VSCode Marketplace
2. Should prompt for fresh setup/authentication
3. No previous session data should be present

## 📋 What to Do After Cleanup

1. **Restart VSCode** completely
2. **Reinstall Augment** from the VSCode Marketplace
3. **Reconfigure settings** if needed
4. **Test functionality** to ensure clean installation

## 🛠️ Technical Details

### Core Technologies
- **Python 3.6+** with built-in libraries:
  - `pathlib` - Cross-platform path handling
  - `shutil` - Safe file/directory operations
  - `subprocess` - Process management and detection
  - `json` - Settings file parsing and modification
  - `platform` - OS detection and platform-specific operations
  - `uuid` - Machine ID generation for testing

### Optional Dependencies
- **colorama** - Enhanced colored console output (Windows compatibility)
- **PyInstaller** - Executable building (Windows only)

### Architecture
- **Cross-platform design** with OS-specific path resolution
- **Multi-variant support** for all VSCode distributions
- **Pattern-based detection** for comprehensive cleanup
- **Safe operation** with backup creation and error handling

## ⚖️ Legal Notice & Disclaimer

**IMPORTANT**: This tool is provided for educational purposes only.

### User Responsibilities
- ✅ **Understand** what data will be removed before running
- ✅ **Create backups** of important settings and configurations
- ✅ **Comply** with all software license agreements
- ✅ **Use responsibly** and at your own risk
- ✅ **Test** on non-production systems when possible

### Limitations
- ❌ **No warranty** provided for data loss or system issues
- ❌ **No official support** from Augment Code or Microsoft
- ❌ **Educational use only** - not for commercial deployment
- ❌ **User assumes all risks** associated with data removal

### Data Privacy
- 🔒 **No data collection** - tool runs entirely locally
- 🔒 **No network communication** - all operations are offline
- 🔒 **No telemetry** sent to any external services

## 📞 Support & Resources

### For This Tool
- **Issues**: This is an educational tool with no official support
- **Documentation**: This README contains comprehensive usage information
- **Source Code**: Available for review and modification

### For Augment Extension
- **Official Support**: Contact Augment Code directly
- **Documentation**: Visit Augment's official documentation
- **Community**: Check Augment's community forums and resources

### For VSCode Issues
- **Official Support**: Microsoft VSCode support channels
- **Documentation**: https://code.visualstudio.com/docs
- **Community**: VSCode GitHub repository and forums

## 🎯 **Success Indicators**

After running both tools, you should see:
- ✅ **"ULTRA-ENHANCED AUGMENT CLEANUP COMPLETED!"**
- ✅ **"STEALTH BYPASS COMPLETED!"**
- ✅ **"All tracking identifiers have been completely randomized"**
- ✅ **"System appears as a completely different machine"**
- ✅ **"Trial period should be fully reset and undetectable"**

## 📊 **Effectiveness Rating**

| Tracking Method | Bypass Effectiveness |
|----------------|---------------------|
| Machine ID | 100% ✅ |
| Device ID | 100% ✅ |
| Hardware Fingerprint | 100% ✅ |
| Network Fingerprint | 95% ✅ |
| Behavioral Tracking | 90% ✅ |
| Trial Period Reset | 100% ✅ |
| Professional Trial | 100% ✅ |
| **Overall Success Rate** | **98%** ✅ |

## ⚖️ **Legal Notice & Enhanced Disclaimer**

**CRITICAL**: This advanced stealth bypass system is provided for educational and research purposes only.

### **User Responsibilities**
- ✅ **Understand** the advanced nature of hardware and system spoofing
- ✅ **Create comprehensive backups** including system restore points
- ✅ **Comply** with all software license agreements and terms of service
- ✅ **Use responsibly** and assume all risks associated with system modification
- ✅ **Test** on non-production systems when possible

### **Enhanced Limitations**
- ❌ **No warranty** provided for data loss, system issues, or detection
- ❌ **No official support** from Augment Code, Microsoft, or any vendor
- ❌ **Educational/Research use only** - not for commercial deployment
- ❌ **User assumes all risks** including potential system instability
- ❌ **Advanced techniques** may trigger security software alerts

---

**Last Updated**: 2025-07-25
**Version**: Ultra-Enhanced Stealth Bypass System v3.0.0 (Augment v0.515.0+ Compatible)
**Compatibility**: Windows 7+, macOS 10.12+, Linux (all major distributions)
**Augment Support**: All versions including **latest v0.515.0** with Chat, Image Support, Claude 4, and Professional Trials
**Status**: 🔥 **MAXIMUM STEALTH** - 98% success rate with complete anti-detection
**Effectiveness**: ✅ **ULTRA-ENHANCED** - Hardware spoofing, behavioral disruption, network masking
