#!/usr/bin/env python3
"""
Manual Augment Cleanup Tool
For when the main cleaner doesn't catch everything
"""

import os
import json
import platform
import shutil
from pathlib import Path

def manual_cleanup():
    """Perform manual cleanup of remaining Augment settings"""
    user_home = Path.home()
    system = platform.system()
    
    # Initialize platform-specific paths
    if system == "Windows":
        appdata = Path(os.environ.get('APPDATA', ''))
        localappdata = Path(os.environ.get('LOCALAPPDATA', ''))
    elif system == "Darwin":  # macOS
        appdata = user_home / "Library" / "Application Support"
        localappdata = user_home / "Library" / "Caches"
    elif system == "Linux":
        appdata = user_home / ".config"
        localappdata = user_home / ".cache"
    else:
        print(f"Unsupported operating system: {system}")
        return

    print("🧹 MANUAL AUGMENT CLEANUP TOOL")
    print("=" * 50)
    print("This tool will manually remove remaining Augment settings")
    print("⚠️  Make sure VSCode is completely closed!")
    print()
    
    response = input("Continue with manual cleanup? (y/N): ")
    if response.lower() != 'y':
        print("❌ Cleanup cancelled.")
        return

    cleaned_items = []

    # 1. Clean settings.json files
    print("\n1️⃣ Cleaning settings.json files...")
    settings_files = [
        appdata / "Code" / "User" / "settings.json",
        appdata / "Code - Insiders" / "User" / "settings.json",
        appdata / "VSCodium" / "User" / "settings.json",
        appdata / "Cursor" / "User" / "settings.json",
        user_home / ".vscode" / "settings.json",
        user_home / ".vscode-insiders" / "settings.json",
        user_home / ".vscodium" / "settings.json",
        user_home / ".cursor" / "settings.json",
    ]
    
    for settings_file in settings_files:
        if settings_file.exists():
            removed_count = clean_settings_file(settings_file)
            if removed_count > 0:
                cleaned_items.append(f"Removed {removed_count} settings from {settings_file.name}")

    # 2. Clean globalStorage directories
    print("\n2️⃣ Cleaning globalStorage directories...")
    storage_dirs = [
        appdata / "Code" / "User" / "globalStorage",
        appdata / "Code - Insiders" / "User" / "globalStorage",
        appdata / "VSCodium" / "User" / "globalStorage",
        appdata / "Cursor" / "User" / "globalStorage",
    ]
    
    for storage_dir in storage_dirs:
        if storage_dir.exists():
            removed_count = clean_storage_directory(storage_dir)
            if removed_count > 0:
                cleaned_items.append(f"Removed {removed_count} storage items from {storage_dir}")

    # 3. Clean workspaceStorage directories
    print("\n3️⃣ Cleaning workspaceStorage directories...")
    workspace_dirs = [
        appdata / "Code" / "User" / "workspaceStorage",
        appdata / "Code - Insiders" / "User" / "workspaceStorage",
        appdata / "VSCodium" / "User" / "workspaceStorage",
        appdata / "Cursor" / "User" / "workspaceStorage",
    ]
    
    for workspace_dir in workspace_dirs:
        if workspace_dir.exists():
            removed_count = clean_workspace_directory(workspace_dir)
            if removed_count > 0:
                cleaned_items.append(f"Removed {removed_count} workspace items from {workspace_dir}")

    # 4. Clean extension directories
    print("\n4️⃣ Cleaning extension directories...")
    extension_dirs = [
        user_home / ".vscode" / "extensions",
        user_home / ".vscode-insiders" / "extensions",
        user_home / ".vscodium" / "extensions",
        user_home / ".cursor" / "extensions",
    ]
    
    for ext_dir in extension_dirs:
        if ext_dir.exists():
            removed_count = clean_extension_directory(ext_dir)
            if removed_count > 0:
                cleaned_items.append(f"Removed {removed_count} extensions from {ext_dir}")

    # Report results
    print("\n" + "=" * 50)
    if cleaned_items:
        print("✅ MANUAL CLEANUP COMPLETED!")
        print("Items cleaned:")
        for item in cleaned_items:
            print(f"  • {item}")
    else:
        print("ℹ️  No additional Augment items found to clean.")
    
    print("\n🔄 Please restart VSCode and check if Augment settings are gone.")
    print("=" * 50)

def clean_settings_file(settings_file):
    """Clean Augment settings from a settings.json file"""
    try:
        # Backup first
        backup_file = settings_file.with_suffix('.json.backup')
        shutil.copy2(settings_file, backup_file)
        
        with open(settings_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        original_count = len(data)
        
        # Remove any keys containing 'augment' (case-insensitive)
        keys_to_remove = []
        for key in data.keys():
            if 'augment' in key.lower():
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del data[key]
            print(f"    Removed: {key}")
        
        if keys_to_remove:
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
        
        return len(keys_to_remove)
    except Exception as e:
        print(f"    Error cleaning {settings_file}: {e}")
        return 0

def clean_storage_directory(storage_dir):
    """Clean Augment items from storage directory"""
    try:
        removed_count = 0
        for item in storage_dir.iterdir():
            if 'augment' in item.name.lower():
                print(f"    Removing: {item.name}")
                if item.is_dir():
                    shutil.rmtree(item)
                else:
                    item.unlink()
                removed_count += 1
        return removed_count
    except Exception as e:
        print(f"    Error cleaning {storage_dir}: {e}")
        return 0

def clean_workspace_directory(workspace_dir):
    """Clean Augment items from workspace storage directory"""
    try:
        removed_count = 0
        for item in workspace_dir.iterdir():
            if item.is_dir():
                # Check if workspace contains Augment data
                state_file = item / "state.vscdb"
                if state_file.exists():
                    try:
                        with open(state_file, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                            if 'augment' in content.lower():
                                print(f"    Removing workspace: {item.name}")
                                shutil.rmtree(item)
                                removed_count += 1
                    except:
                        pass
        return removed_count
    except Exception as e:
        print(f"    Error cleaning {workspace_dir}: {e}")
        return 0

def clean_extension_directory(ext_dir):
    """Clean Augment extensions from extension directory"""
    try:
        removed_count = 0
        for item in ext_dir.iterdir():
            if item.is_dir() and 'augment' in item.name.lower():
                print(f"    Removing extension: {item.name}")
                shutil.rmtree(item)
                removed_count += 1
        return removed_count
    except Exception as e:
        print(f"    Error cleaning {ext_dir}: {e}")
        return 0

if __name__ == "__main__":
    manual_cleanup()
    input("\nPress Enter to exit...")
