#!/usr/bin/env python3
"""
Augment Settings Diagnostic Tool
Helps identify remaining Augment settings in VSCode after cleanup
"""

import os
import json
import platform
from pathlib import Path

def find_augment_settings():
    """Find all remaining Augment-related settings in VSCode"""
    user_home = Path.home()
    system = platform.system()
    
    # Initialize platform-specific paths
    if system == "Windows":
        appdata = Path(os.environ.get('APPDATA', ''))
        localappdata = Path(os.environ.get('LOCALAPPDATA', ''))
    elif system == "Darwin":  # macOS
        appdata = user_home / "Library" / "Application Support"
        localappdata = user_home / "Library" / "Caches"
    elif system == "Linux":
        appdata = user_home / ".config"
        localappdata = user_home / ".cache"
    else:
        print(f"Unsupported operating system: {system}")
        return

    print("🔍 AUGMENT SETTINGS DIAGNOSTIC TOOL")
    print("=" * 50)
    print(f"Platform: {system}")
    print()

    # VSCode variants to check
    variants = ["Code", "Code - Insiders", "VSCodium", "Cursor", "Code - OSS"]
    
    found_settings = []
    
    for variant in variants:
        print(f"Checking {variant}...")
        
        # Check main settings.json
        settings_file = appdata / variant / "User" / "settings.json"
        if settings_file.exists():
            augment_settings = check_json_for_augment(settings_file)
            if augment_settings:
                found_settings.extend([(settings_file, key, value) for key, value in augment_settings])
        
        # Check globalStorage
        global_storage = appdata / variant / "User" / "globalStorage"
        if global_storage.exists():
            augment_storage = check_storage_for_augment(global_storage)
            if augment_storage:
                found_settings.extend([(item, "storage", "directory") for item in augment_storage])
    
    # Check common locations
    common_settings = [
        user_home / ".vscode" / "settings.json",
        user_home / ".vscode-insiders" / "settings.json", 
        user_home / ".vscodium" / "settings.json",
        user_home / ".cursor" / "settings.json",
    ]
    
    for settings_file in common_settings:
        if settings_file.exists():
            augment_settings = check_json_for_augment(settings_file)
            if augment_settings:
                found_settings.extend([(settings_file, key, value) for key, value in augment_settings])
    
    # Report findings
    print("\n" + "=" * 50)
    if found_settings:
        print("❌ FOUND REMAINING AUGMENT SETTINGS:")
        print("=" * 50)
        for location, key, value in found_settings:
            print(f"📁 File: {location}")
            print(f"🔑 Key: {key}")
            print(f"💾 Value: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")
            print("-" * 30)
        
        print(f"\n📊 Total found: {len(found_settings)} Augment-related settings")
        print("\n💡 RECOMMENDATION:")
        print("Run the enhanced cleaner again or manually remove these settings.")
        
    else:
        print("✅ NO AUGMENT SETTINGS FOUND!")
        print("The cleanup appears to be complete.")
    
    print("\n" + "=" * 50)

def check_json_for_augment(json_file):
    """Check a JSON file for Augment-related settings"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        augment_settings = []
        for key, value in data.items():
            if 'augment' in key.lower():
                augment_settings.append((key, value))
        
        return augment_settings
    except:
        return []

def check_storage_for_augment(storage_dir):
    """Check storage directory for Augment-related items"""
    try:
        augment_items = []
        for item in storage_dir.iterdir():
            if 'augment' in item.name.lower():
                augment_items.append(item)
        return augment_items
    except:
        return []

if __name__ == "__main__":
    find_augment_settings()
    input("\nPress Enter to exit...")
