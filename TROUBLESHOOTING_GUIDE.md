# 🔧 Augment Cleanup Troubleshooting Guide

## ❌ Problem: Still seeing Augment settings in VSCode after cleanup

If you're still seeing Augment-related settings when searching for "augment" in VSCode settings, follow this comprehensive troubleshooting guide.

## 🔍 Step 1: Identify What's Still There

### Run the Diagnostic Tool
```bash
python augment_diagnostic.py
```

This will show you exactly what Augment settings remain and where they're located.

### Manual Check in VSCode
1. Open VSCode
2. Go to `File > Preferences > Settings`
3. Search for "augment"
4. Note down exactly what settings you see

## 🧹 Step 2: Enhanced Cleanup Methods

### Method A: Run Enhanced Cleaner Again
```bash
python augment_cleaner.py
```

**Important:**
- ✅ **Close ALL VSCode windows** before running
- ✅ **Answer "y" (yes)** when prompted
- ✅ **Wait for complete process** to finish
- ✅ **Restart your computer** after cleanup

### Method B: Manual Cleanup Tool
```bash
python manual_augment_cleanup.py
```

This tool performs more aggressive manual cleanup of remaining settings.

### Method C: Direct File Editing

#### Windows
1. Close VSCode completely
2. Navigate to: `%APPDATA%\Code\User\settings.json`
3. Open in Notepad
4. Remove any lines containing "augment" (case-insensitive)
5. Save the file

#### macOS
1. Close VSCode completely
2. Navigate to: `~/Library/Application Support/Code/User/settings.json`
3. Open in TextEdit
4. Remove any lines containing "augment"
5. Save the file

#### Linux
1. Close VSCode completely
2. Navigate to: `~/.config/Code/User/settings.json`
3. Open in text editor
4. Remove any lines containing "augment"
5. Save the file

## 🔄 Step 3: Clear All VSCode Data (Nuclear Option)

If settings persist, you can reset VSCode completely:

### Windows
```cmd
# Close VSCode first
rmdir /s "%APPDATA%\Code"
rmdir /s "%USERPROFILE%\.vscode"
```

### macOS
```bash
# Close VSCode first
rm -rf ~/Library/Application\ Support/Code
rm -rf ~/.vscode
```

### Linux
```bash
# Close VSCode first
rm -rf ~/.config/Code
rm -rf ~/.vscode
```

**⚠️ Warning:** This removes ALL VSCode settings, not just Augment!

## 🎯 Step 4: Check Multiple VSCode Variants

You might have multiple VSCode installations:

### Check These Locations:

#### Windows
- `%APPDATA%\Code\` (Standard VSCode)
- `%APPDATA%\Code - Insiders\` (Insiders)
- `%APPDATA%\VSCodium\` (VSCodium)
- `%APPDATA%\Cursor\` (Cursor)

#### macOS
- `~/Library/Application Support/Code/`
- `~/Library/Application Support/Code - Insiders/`
- `~/Library/Application Support/VSCodium/`
- `~/Library/Application Support/Cursor/`

#### Linux
- `~/.config/Code/`
- `~/.config/Code - Insiders/`
- `~/.config/VSCodium/`
- `~/.config/Cursor/`

## 🔍 Step 5: Check Workspace-Specific Settings

Augment settings might be in project-specific configurations:

### Look for these files in your projects:
- `.vscode/settings.json`
- `.vscode/launch.json`
- `.vscode/tasks.json`
- `augment.json`
- `.augment*`

### Search command:
```bash
# Windows (PowerShell)
Get-ChildItem -Recurse -Name "*.json" | Select-String -Pattern "augment"

# macOS/Linux
find . -name "*.json" -exec grep -l "augment" {} \;
```

## 🔄 Step 6: Disable Settings Sync

If you have VSCode Settings Sync enabled:

1. Open VSCode
2. Go to `File > Preferences > Settings Sync`
3. **Turn off** Settings Sync
4. **Clear** synced data
5. Run cleanup again
6. Re-enable sync if desired

## 🧪 Step 7: Verification Steps

After cleanup:

1. **Restart your computer** (important!)
2. **Open VSCode**
3. **Check Settings**: `File > Preferences > Settings`
4. **Search for "augment"** - should return no results
5. **Check Extensions**: Should not see Augment installed
6. **Check Command Palette**: `Ctrl+Shift+P` and search "augment" - should find nothing

## 🆘 Step 8: Last Resort Solutions

### Option A: Fresh VSCode Installation
1. **Uninstall VSCode** completely
2. **Delete all VSCode folders** (see Step 3)
3. **Restart computer**
4. **Reinstall VSCode** from scratch

### Option B: Use Different VSCode Variant
- Try **VSCode Insiders** instead of regular VSCode
- Try **VSCodium** (open-source version)
- Try **Cursor** (AI-powered editor)

## 📋 Common Causes of Persistent Settings

1. **VSCode still running** during cleanup
2. **Multiple VSCode variants** installed
3. **Settings Sync** re-downloading configurations
4. **Workspace-specific** settings in projects
5. **Cached settings** not cleared
6. **Extension remnants** in globalStorage
7. **Browser-based** VSCode (vscode.dev) settings

## 🎯 Quick Fix Commands

### Windows (PowerShell as Administrator)
```powershell
# Stop all VSCode processes
Get-Process | Where-Object {$_.ProcessName -like "*code*"} | Stop-Process -Force

# Remove settings
Remove-Item -Recurse -Force "$env:APPDATA\Code\User\settings.json" -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force "$env:USERPROFILE\.vscode" -ErrorAction SilentlyContinue
```

### macOS/Linux
```bash
# Stop all VSCode processes
pkill -f code

# Remove settings
rm -f ~/Library/Application\ Support/Code/User/settings.json  # macOS
rm -f ~/.config/Code/User/settings.json  # Linux
rm -rf ~/.vscode
```

## 📞 Still Having Issues?

If Augment settings persist after all these steps:

1. **Run the diagnostic tool** and share the output
2. **Check if you have VSCode Settings Sync** enabled
3. **Verify you're checking the correct VSCode variant**
4. **Consider using a different VSCode installation**

The enhanced tools should catch all remaining Augment configurations!
