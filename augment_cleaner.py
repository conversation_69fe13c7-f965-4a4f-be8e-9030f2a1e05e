#!/usr/bin/env python3
"""
Augment VSCode Cleaner
Educational tool to clean VSCode and Augment extension data for fresh installations.
Enhanced with cross-platform support and multiple VS Code variant detection.
"""

import os
import shutil
import sys
import json
import platform
import uuid
from pathlib import Path
import subprocess
import time

# Console colors (with fallback)
try:
    from colorama import init, Fore, Style
    init()  # Initialize colorama for Windows support

    def info_color(msg: str) -> None:
        """Print an info message in blue"""
        print(f"{Fore.BLUE}[INFO]{Style.RESET_ALL} {msg}")

    def success_color(msg: str) -> None:
        """Print a success message in green"""
        print(f"{Fore.GREEN}[SUCCESS]{Style.RESET_ALL} {msg}")

    def warning_color(msg: str) -> None:
        """Print a warning message in yellow"""
        print(f"{Fore.YELLOW}[WARNING]{Style.RESET_ALL} {msg}")

    def error_color(msg: str) -> None:
        """Print an error message in red"""
        print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} {msg}")

except ImportError:
    # Fallback if colorama is not installed
    def info_color(msg: str) -> None:
        print(f"[INFO] {msg}")

    def success_color(msg: str) -> None:
        print(f"[SUCCESS] {msg}")

    def warning_color(msg: str) -> None:
        print(f"[WARNING] {msg}")

    def error_color(msg: str) -> None:
        print(f"[ERROR] {msg}")

class AugmentCleaner:
    def __init__(self):
        self.user_home = Path.home()
        self.system = platform.system()

        # Initialize platform-specific paths
        if self.system == "Windows":
            self.appdata = Path(os.environ.get('APPDATA', ''))
            self.localappdata = Path(os.environ.get('LOCALAPPDATA', ''))
        elif self.system == "Darwin":  # macOS
            self.appdata = self.user_home / "Library" / "Application Support"
            self.localappdata = self.user_home / "Library" / "Caches"
        elif self.system == "Linux":
            self.appdata = self.user_home / ".config"
            self.localappdata = self.user_home / ".cache"
        else:
            error_color(f"Unsupported operating system: {self.system}")
            sys.exit(1)

        # Get comprehensive VSCode paths for all variants
        self.vscode_paths = self.get_all_vscode_paths()

        # Initialize pattern definitions
        self._initialize_patterns()

    def get_vscode_variants(self):
        """Get list of VS Code variants to check for"""
        return [
            "Code",           # Standard VS Code
            "Code - Insiders", # VS Code Insiders
            "VSCodium",       # VSCodium (open source)
            "Cursor",         # Cursor (AI-powered editor)
            "Code - OSS",     # VS Code OSS
        ]

    def get_all_vscode_paths(self):
        """Get all possible VSCode installation paths for current platform"""
        variants = self.get_vscode_variants()
        paths = []

        for variant in variants:
            if self.system == "Windows":
                # User data directories
                user_dir = self.appdata / variant
                paths.append(user_dir)

                # Installation directories
                install_dir = self.localappdata / "Programs" / f"Microsoft {variant}"
                paths.append(install_dir)

                # Extensions directories
                ext_dir = self.user_home / f".{variant.lower().replace(' ', '-')}"
                paths.append(ext_dir)

            elif self.system == "Darwin":  # macOS
                user_dir = self.appdata / variant
                paths.append(user_dir)

                # Extensions directory
                ext_dir = self.user_home / f".{variant.lower().replace(' ', '-')}"
                paths.append(ext_dir)

            elif self.system == "Linux":
                user_dir = self.appdata / variant
                paths.append(user_dir)

                # Extensions directory
                ext_dir = self.user_home / f".{variant.lower().replace(' ', '-')}"
                paths.append(ext_dir)

        # Add common extension paths
        paths.extend([
            self.user_home / ".vscode",
            self.user_home / ".vscode-insiders",
            self.user_home / ".vscodium",
            self.user_home / ".cursor",
        ])

        return paths

    def find_active_vscode_installations(self):
        """Find all active VSCode installations on the system"""
        active_installations = []

        for path in self.vscode_paths:
            if path.exists():
                # Check if it's a valid VSCode installation
                user_dir = path / "User" if (path / "User").exists() else path
                if (user_dir / "globalStorage").exists() or (user_dir / "extensions").exists():
                    active_installations.append(path)
                    info_color(f"Found VSCode installation: {path}")

        return active_installations

    def _initialize_patterns(self):
        """Initialize all pattern definitions for Augment detection"""
        # Comprehensive Augment-specific patterns (Enhanced for v0.515.0+)
        self.augment_patterns = [
            # Core Augment patterns
            "augment",
            "Augment",
            "AUGMENT",

            # Official extension identifier (from VSCode Marketplace)
            "augment.vscode-augment",
            "augment.vscode",

            # Augment Code specific
            "augment-code",
            "augmentcode",
            "AugmentCode",
            "augment.code",
            "augment_code",
            "augment-computing",
            "augmentcomputing",
            "AugmentComputing",

            # Extension ID patterns (common VSCode extension naming)
            "augment-vscode",
            "vscode-augment",
            "augment-extension",
            "augment-ai",
            "augment-assistant",

            # AI coding assistant patterns
            "augment-copilot",
            "augment-intellisense",
            "augment-autocomplete",
            "augment-suggestions",
            "augment-completion",
            "augment-pair-programmer",

            # Company/Publisher patterns
            "augmentlabs",
            "augment-labs",
            "augment.labs",
            "augment_labs",

            # File extension patterns
            ".augment",
            "augment.json",
            "augment.db",
            "augment.sqlite",
            "augment.log",

            # Enhanced patterns for v0.502.0+ compatibility
            "augment-agent",
            "augment-context",
            "augment-engine",
            "augment-retrieval",
            "augment-embeddings",
            "augment-indexing",
            "augment-workspace",
            "augment-project",
            "augment-codebase",
            "augment-analysis",

            # Latest v0.504.0+ features
            "augment-nextedit",
            "augment-next-edit",
            "augment-mcp",
            "augment-protocol",
            "augment-tools",
            "augment-integrations",
            "augment-smart-apply",
            "augment-smartapply",
            "augment-instructions",
            "augment-completions",
            "augment-professional",
            "augment-trial",
            "augment-subscription",

            # Latest v0.515.0+ patterns (2025 updates)
            "augment-chat",
            "augment-image-support",
            "augment-multimodal",
            "augment-claude4",
            "augment-anthropic",
            "augment-vision",
            "augment-screenshot",
            "augment-context-engine",
            "augment-codebase-aware",
            "augment-professional-trial",
            "augment-7day-trial",
            "augment-free-trial",
            "augment-trial-tracking",
            "augment-usage-metrics",
            "augment-billing",
            "augment-payment",
            "augment-license",
            "augment-activation",
        ]

        # Comprehensive telemetry and configuration keys (enhanced from reference module)
        self.telemetry_keys = [
            # Core VSCode telemetry identifiers (critical for Augment compatibility)
            "telemetry.machineId",
            "telemetry.devDeviceId",
            "telemetry.sessionId",
            "telemetry.sqmId",
            "telemetry.installationId",
            "telemetry.enableTelemetry",
            "telemetry.enableCrashReporter",
            "telemetry.telemetryLevel",

            # Additional VSCode identifiers (may be used by extensions)
            "machineId",
            "devDeviceId",
            "sessionId",
            "sqmId",
            "installationId",

            # Extension-specific identifiers that may be used by Augment
            "vscode.machineId",
            "vscode.sessionId",
            "vscode.deviceId",
            "microsoft.machineId",
            "microsoft.sessionId",
            "microsoft.deviceId",

            # GitHub Copilot related (often used by AI extensions)
            "github.machineId",
            "github.sessionId",
            "github.deviceId",
            "copilot.machineId",
            "copilot.sessionId",
            "copilot.deviceId",

            # Augment-specific identifiers and telemetry
            "augment.machineId",
            "augment.sessionId",
            "augment.deviceId",
            "augment.installationId",
            "augment.telemetry.enabled",
            "augment.telemetry.level",
            "augment.analytics.enabled",
            "augment.metrics.enabled",
            "augment.usage.tracking",
            "augment.crash.reporting",
            "augment.error.reporting",
            "augment.performance.tracking",

            # Authentication and session keys
            "augment.auth.token",
            "augment.auth.refreshToken",
            "augment.auth.accessToken",
            "augment.session.id",
            "augment.user.id",
            "augment.user.email",
            "augment.user.profile",
            "augment.account.id",
            "augment.login.state",
            "augment.credentials",

            # Configuration keys
            "augment.enabled",
            "augment.autoComplete.enabled",
            "augment.suggestions.enabled",
            "augment.inline.suggestions",
            "augment.code.completion",
            "augment.ai.model",
            "augment.api.endpoint",
            "augment.api.key",
            "augment.server.url",

            # Cache and storage keys
            "augment.cache.enabled",
            "augment.cache.size",
            "augment.storage.path",
            "augment.workspace.settings",
            "augment.project.config",

            # Feature flags and experiments
            "augment.experiments.enabled",
            "augment.features.beta",
            "augment.preview.features",
            "augment.experimental.mode",

            # Privacy and data collection
            "augment.data.collection",
            "augment.privacy.mode",
            "augment.anonymous.usage",
            "augment.diagnostic.data",

            # Update and version tracking
            "augment.update.channel",
            "augment.version.check",
            "augment.auto.update",
            "augment.last.version",
            "augment.version.current",
            "augment.version.0.502.0",

            # Common AI assistant patterns
            "ai.augment.enabled",
            "assistant.augment.config",
            "copilot.augment.integration",

            # Enhanced patterns for v0.502.0+ (context engine, retrieval, etc.)
            "augment.context.engine",
            "augment.retrieval.enabled",
            "augment.embeddings.cache",
            "augment.indexing.status",
            "augment.workspace.analysis",
            "augment.codebase.context",
            "augment.agent.config",
            "augment.llm.model",
            "augment.llm.provider",
            "augment.claude.config",
            "augment.openai.config",
            "augment.anthropic.config",

            # Real-time features
            "augment.realtime.enabled",
            "augment.streaming.enabled",
            "augment.websocket.config",
            "augment.live.updates",

            # Advanced features
            "augment.multimodal.enabled",
            "augment.code.understanding",
            "augment.semantic.search",
            "augment.vector.database",

            # Latest v0.504.0+ features
            "augment.agent.enabled",
            "augment.agent.config",
            "augment.agent.session",
            "augment.agent.history",
            "augment.nextedit.enabled",
            "augment.nextedit.config",
            "augment.nextedit.history",
            "augment.mcp.enabled",
            "augment.mcp.servers",
            "augment.mcp.tools",
            "augment.mcp.protocol",
            "augment.smartapply.enabled",
            "augment.smartapply.config",
            "augment.instructions.enabled",
            "augment.instructions.history",
            "augment.completions.enabled",
            "augment.completions.config",
            "augment.professional.enabled",
            "augment.trial.status",
            "augment.trial.remaining",
            "augment.subscription.status",
            "augment.subscription.plan",
            "augment.version.0.504.0",
            "augment.version.current",

            # Latest v0.515.0+ tracking keys (2025 enhanced tracking)
            "augment.version.0.515.0",
            "augment.chat.enabled",
            "augment.chat.history",
            "augment.chat.sessions",
            "augment.image.support",
            "augment.multimodal.enabled",
            "augment.vision.enabled",
            "augment.claude4.config",
            "augment.anthropic.key",
            "augment.professional.trial.start",
            "augment.professional.trial.end",
            "augment.7day.trial.activated",
            "augment.free.trial.used",
            "augment.trial.device.fingerprint",
            "augment.hardware.signature",
            "augment.system.fingerprint",
            "augment.network.fingerprint",
            "augment.browser.fingerprint",
            "augment.usage.analytics",
            "augment.feature.usage",
            "augment.activation.timestamp",
            "augment.first.install.date",
            "augment.last.active.date",
            "augment.billing.cycle",
            "augment.payment.method",
            "augment.license.key",
            "augment.activation.code",
            "augment.device.trust.score",
            "augment.user.behavior.pattern",
            "augment.codebase.fingerprint",
            "augment.workspace.signature",
        ]

        # GlobalStorage and WorkspaceStorage key patterns (Enhanced for v0.502.0+)
        self.storage_patterns = [
            # Augment-specific storage keys
            "augment",
            "augment.vscode-augment",
            "augment-code",
            "augment-session",
            "augment-auth",
            "augment-user",
            "augment-cache",
            "augment-settings",
            "augment-workspace",
            "augment-telemetry",
            "augment-analytics",
            "augment-metrics",
            "augment-experiments",
            "augment-features",

            # Authentication storage patterns
            "auth.augment",
            "session.augment",
            "token.augment",
            "credentials.augment",
            "login.augment",

            # Cache storage patterns
            "cache.augment",
            "temp.augment",
            "data.augment",
            "storage.augment",

            # Enhanced storage patterns for v0.502.0+
            "augment-context",
            "augment-embeddings",
            "augment-vectors",
            "augment-index",
            "augment-retrieval",
            "augment-engine",
            "augment-agent",
            "augment-llm",
            "augment-model",
            "augment-provider",
            "context.augment",
            "embeddings.augment",
            "vectors.augment",
            "index.augment",
            "retrieval.augment",
            "engine.augment",
            "agent.augment",

            # Latest v0.504.0+ storage patterns
            "augment-nextedit",
            "augment-next-edit",
            "augment-mcp",
            "augment-protocol",
            "augment-tools",
            "augment-integrations",
            "augment-smartapply",
            "augment-smart-apply",
            "augment-instructions",
            "augment-completions",
            "augment-professional",
            "augment-trial",
            "augment-subscription",
            "nextedit.augment",
            "mcp.augment",
            "protocol.augment",
            "tools.augment",
            "integrations.augment",
            "smartapply.augment",
            "instructions.augment",
            "completions.augment",
            "professional.augment",
            "trial.augment",
            "subscription.augment",
        ]
        
    def log(self, message, level="info"):
        """Print timestamped log message with color coding"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")

        if level == "info":
            info_color(f"[{timestamp}] {message}")
        elif level == "success":
            success_color(f"[{timestamp}] {message}")
        elif level == "warning":
            warning_color(f"[{timestamp}] {message}")
        elif level == "error":
            error_color(f"[{timestamp}] {message}")
        else:
            print(f"[{timestamp}] {message}")

    def backup_file(self, file_path):
        """Create a backup of a file before modification"""
        if not file_path.exists():
            self.log(f"File not found for backup: {file_path}", "warning")
            return None

        backup_path = Path(f"{file_path}.backup.{int(time.time())}")
        try:
            shutil.copy2(file_path, backup_path)
            self.log(f"Created backup: {backup_path}", "success")
            return backup_path
        except Exception as e:
            self.log(f"Failed to create backup: {e}", "error")
            return None

    def generate_machine_id(self):
        """Generate a random 64-character hex string for machineId"""
        return uuid.uuid4().hex + uuid.uuid4().hex

    def generate_device_id(self):
        """Generate a random UUID v4 for devDeviceId"""
        return str(uuid.uuid4())

    def generate_hardware_signature(self):
        """Generate a fake hardware signature to replace real one"""
        import random
        import string
        # Generate fake CPU signature
        cpu_sig = ''.join(random.choices(string.ascii_uppercase + string.digits, k=16))
        # Generate fake GPU signature
        gpu_sig = ''.join(random.choices(string.ascii_uppercase + string.digits, k=12))
        # Generate fake motherboard signature
        mb_sig = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
        return f"{cpu_sig}-{gpu_sig}-{mb_sig}"

    def spoof_system_identifiers(self):
        """Replace system identifiers with fake ones to prevent tracking"""
        try:
            # Generate new fake identifiers
            new_machine_id = self.generate_machine_id()
            new_device_id = self.generate_device_id()
            new_hw_signature = self.generate_hardware_signature()

            self.log(f"Generated new machine ID: {new_machine_id[:16]}...", "success")
            self.log(f"Generated new device ID: {new_device_id}", "success")
            self.log(f"Generated new hardware signature: {new_hw_signature}", "success")

            return new_machine_id, new_device_id, new_hw_signature
        except Exception as e:
            self.log(f"Error generating fake identifiers: {e}", "error")
            return None, None, None
        
    def is_vscode_running(self):
        """Check if VSCode is currently running (cross-platform)"""
        try:
            if self.system == "Windows":
                result = subprocess.run(['tasklist'], capture_output=True, text=True, shell=True)
                vscode_processes = ['Code.exe', 'code.exe', 'CodeHelper.exe', 'cursor.exe', 'vscodium.exe']
                return any(proc in result.stdout for proc in vscode_processes)

            elif self.system in ["Darwin", "Linux"]:
                result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
                vscode_processes = ['code', 'Code', 'cursor', 'vscodium', 'code-insiders']
                return any(proc in result.stdout for proc in vscode_processes)

        except Exception as e:
            self.log(f"Error checking VSCode processes: {e}", "warning")
            return False

    def kill_vscode_processes(self):
        """Terminate VSCode processes (cross-platform)"""
        if self.system == "Windows":
            processes = ['Code.exe', 'code.exe', 'CodeHelper.exe', 'cursor.exe', 'vscodium.exe']
            for process in processes:
                try:
                    subprocess.run(['taskkill', '/F', '/IM', process],
                                 capture_output=True, shell=True)
                    self.log(f"Terminated {process}")
                except:
                    pass

        elif self.system in ["Darwin", "Linux"]:
            processes = ['code', 'Code', 'cursor', 'vscodium', 'code-insiders']
            for process in processes:
                try:
                    subprocess.run(['pkill', '-f', process], capture_output=True)
                    self.log(f"Terminated {process}")
                except:
                    pass
                
    def clean_directory(self, path, description=""):
        """Safely remove a directory and its contents"""
        try:
            if path.exists():
                if path.is_file():
                    path.unlink()
                    self.log(f"Removed file: {path} {description}")
                else:
                    shutil.rmtree(path)
                    self.log(f"Removed directory: {path} {description}")
                return True
            else:
                self.log(f"Path not found: {path}")
                return False
        except Exception as e:
            self.log(f"Error removing {path}: {e}")
            return False
            
    def clean_augment_from_extensions(self):
        """Remove Augment extensions from all VSCode variants"""
        # Get all possible extension directories
        extensions_paths = []

        # Add variant-specific extension paths
        for variant in self.get_vscode_variants():
            if self.system == "Windows":
                ext_path = self.appdata / variant / "extensions"
                extensions_paths.append(ext_path)
            elif self.system == "Darwin":
                ext_path = self.appdata / variant / "extensions"
                extensions_paths.append(ext_path)
            elif self.system == "Linux":
                ext_path = self.appdata / variant / "extensions"
                extensions_paths.append(ext_path)

        # Add common extension directories
        extensions_paths.extend([
            self.user_home / ".vscode" / "extensions",
            self.user_home / ".vscode-insiders" / "extensions",
            self.user_home / ".vscodium" / "extensions",
            self.user_home / ".cursor" / "extensions",
        ])

        for ext_path in extensions_paths:
            if ext_path.exists():
                self.log(f"Scanning extensions in: {ext_path}")
                for item in ext_path.iterdir():
                    if any(pattern.lower() in item.name.lower() for pattern in self.augment_patterns):
                        self.clean_directory(item, "(Augment extension)")
                        
    def clean_vscode_settings(self):
        """Clean only Augment-related data from all VSCode variant settings"""
        settings_files = []
        storage_paths = []

        # Get settings files for all variants
        for variant in self.get_vscode_variants():
            variant_base = self.appdata / variant / "User"
            settings_files.extend([
                variant_base / "settings.json",
                variant_base / "keybindings.json",
                variant_base / "tasks.json",
                variant_base / "launch.json"
            ])
            storage_paths.extend([
                variant_base / "globalStorage",
                variant_base / "workspaceStorage"
            ])

        # Also check common VSCode locations
        common_locations = [
            self.user_home / ".vscode" / "settings.json",
            self.user_home / ".vscode-insiders" / "settings.json",
            self.user_home / ".vscodium" / "settings.json",
            self.user_home / ".cursor" / "settings.json",
        ]
        settings_files.extend(common_locations)

        # Clean all settings files
        for settings_file in settings_files:
            if settings_file.exists():
                self.log(f"Cleaning settings: {settings_file}")
                self.clean_json_settings(settings_file)

        # Clean only Augment-related storage directories
        for storage_path in storage_paths:
            if storage_path.exists():
                self.log(f"Cleaning storage: {storage_path}")
                self.clean_augment_from_storage(storage_path)
                    
    def clean_augment_from_storage(self, storage_path):
        """Clean only Augment-related entries from storage directories"""
        try:
            if storage_path.exists() and storage_path.is_dir():
                for item in storage_path.iterdir():
                    # Check against both general patterns and specific storage patterns
                    item_name_lower = item.name.lower()
                    should_remove = False

                    # Check against general Augment patterns
                    if any(pattern.lower() in item_name_lower for pattern in self.augment_patterns):
                        should_remove = True

                    # Check against specific storage patterns
                    if any(pattern.lower() in item_name_lower for pattern in self.storage_patterns):
                        should_remove = True

                    # Check for the official extension ID
                    if "augment.vscode-augment" in item_name_lower:
                        should_remove = True

                    if should_remove:
                        self.clean_directory(item, "(Augment storage data)")

        except Exception as e:
            self.log(f"Error cleaning storage directory {storage_path}: {e}")

    def clean_json_settings(self, json_path):
        """Clean Augment telemetry and configuration entries from JSON settings files"""
        try:
            # Create backup first
            backup_path = self.backup_file(json_path)

            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            original_data = data.copy()

            # Remove Augment-related keys (pattern matching) - more aggressive
            pattern_keys_to_remove = []
            for key in list(data.keys()):
                key_lower = key.lower()
                # Check against all Augment patterns
                if any(pattern.lower() in key_lower for pattern in self.augment_patterns):
                    pattern_keys_to_remove.append(key)
                # Additional specific checks for common VSCode setting patterns
                elif any(term in key_lower for term in ['augment', 'Augment', 'AUGMENT']):
                    pattern_keys_to_remove.append(key)

            # Remove specific known telemetry keys
            telemetry_keys_to_remove = []
            for key in self.telemetry_keys:
                if key in data:
                    telemetry_keys_to_remove.append(key)

            # Also check for nested Augment settings in objects
            nested_keys_to_remove = []
            for key, value in data.items():
                if isinstance(value, dict):
                    # Check if any nested keys contain Augment references
                    for nested_key in list(value.keys()):
                        if any(pattern.lower() in nested_key.lower() for pattern in self.augment_patterns):
                            nested_keys_to_remove.append((key, nested_key))

            all_keys_to_remove = list(set(pattern_keys_to_remove + telemetry_keys_to_remove))

            if all_keys_to_remove or nested_keys_to_remove:
                # Remove top-level keys
                for key in all_keys_to_remove:
                    if key in data:
                        del data[key]
                        self.log(f"Removed Augment setting: {key}")

                # Remove nested keys
                for parent_key, nested_key in nested_keys_to_remove:
                    if parent_key in data and isinstance(data[parent_key], dict):
                        if nested_key in data[parent_key]:
                            del data[parent_key][nested_key]
                            self.log(f"Removed nested Augment setting: {parent_key}.{nested_key}")

                # Write back the cleaned settings
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2)

                total_removed = len(all_keys_to_remove) + len(nested_keys_to_remove)
                self.log(f"Cleaned {total_removed} Augment settings from {json_path.name}")
                if pattern_keys_to_remove:
                    self.log(f"  - Pattern matches: {len(pattern_keys_to_remove)}")
                if telemetry_keys_to_remove:
                    self.log(f"  - Known telemetry keys: {len(telemetry_keys_to_remove)}")
                if nested_keys_to_remove:
                    self.log(f"  - Nested settings: {len(nested_keys_to_remove)}")
            else:
                self.log(f"No Augment settings found in {json_path.name}")

        except Exception as e:
            self.log(f"Error cleaning JSON file {json_path}: {e}", "error")

    def clean_workspace_settings(self):
        """Clean Augment settings from workspace-specific configurations"""
        # Common workspace settings locations
        workspace_patterns = [
            "**/.vscode/settings.json",
            "**/.vscode/launch.json",
            "**/.vscode/tasks.json",
            "**/augment.json",
            "**/.augment*",
        ]

        # Search in common project directories
        search_paths = [
            self.user_home / "Documents",
            self.user_home / "Desktop",
            self.user_home / "Projects",
            Path("C:/") / "Users" / self.user_home.name / "source",
            Path("C:/") / "dev",
            Path("C:/") / "projects",
        ]

        for search_path in search_paths:
            if search_path.exists():
                try:
                    # Look for .vscode folders
                    for vscode_dir in search_path.rglob(".vscode"):
                        if vscode_dir.is_dir():
                            settings_file = vscode_dir / "settings.json"
                            if settings_file.exists():
                                self.log(f"Cleaning workspace settings: {settings_file}")
                                self.clean_json_settings(settings_file)

                    # Look for Augment-specific files
                    for pattern in ["**/augment.json", "**/.augment*"]:
                        for augment_file in search_path.rglob(pattern.split("/")[-1]):
                            if augment_file.is_file():
                                self.log(f"Removing Augment workspace file: {augment_file}")
                                self.clean_directory(augment_file, "(workspace Augment file)")

                except Exception as e:
                    # Skip directories we can't access
                    pass
            
    def clean_registry_entries(self):
        """Clean only Augment-related Windows registry entries"""
        try:
            import winreg

            # Registry paths that might contain Augment data
            registry_paths = [
                (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\VSCode\Extensions"),
                (winreg.HKEY_CURRENT_USER, r"Software\Classes\Applications\Code.exe"),
                (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\ApplicationAssociationToasts"),
            ]

            for hkey, subkey_path in registry_paths:
                try:
                    with winreg.OpenKey(hkey, subkey_path, 0, winreg.KEY_READ | winreg.KEY_WRITE) as key:
                        # Enumerate values and remove Augment-related ones
                        i = 0
                        while True:
                            try:
                                value_name, value_data, value_type = winreg.EnumValue(key, i)
                                if any(pattern.lower() in value_name.lower() for pattern in self.augment_patterns):
                                    winreg.DeleteValue(key, value_name)
                                    self.log(f"Removed registry value: {value_name}")
                                else:
                                    i += 1
                            except WindowsError:
                                break
                except Exception as e:
                    self.log(f"Registry access limited for {subkey_path}: {e}")

        except ImportError:
            self.log("Registry cleaning not available on this system")
        except Exception as e:
            self.log(f"Registry cleaning error: {e}")

    def clean_environment_variables(self):
        """Clean Augment-related environment variables"""
        import os

        env_vars_to_check = list(os.environ.keys())
        for var_name in env_vars_to_check:
            if any(pattern.lower() in var_name.lower() for pattern in self.augment_patterns):
                try:
                    # Note: This only affects the current process, not system-wide
                    del os.environ[var_name]
                    self.log(f"Removed environment variable: {var_name}")
                except:
                    pass
                
    def clean_temp_files(self):
        """Clean temporary files that might contain Augment data"""
        temp_paths = [
            Path(os.environ.get('TEMP', '')),
            Path(os.environ.get('TMP', '')),
            self.localappdata / "Temp",
        ]
        
        for temp_path in temp_paths:
            if temp_path.exists():
                for item in temp_path.iterdir():
                    try:
                        if any(pattern.lower() in item.name.lower() for pattern in self.augment_patterns):
                            self.clean_directory(item, "(temp file)")
                    except:
                        pass  # Skip files that can't be accessed

    def clean_browser_data(self):
        """Clean Augment-related data from browser storage (for web-based components)"""
        browser_paths = [
            # Chrome/Chromium
            self.localappdata / "Google" / "Chrome" / "User Data" / "Default" / "Local Storage",
            self.localappdata / "Google" / "Chrome" / "User Data" / "Default" / "Session Storage",
            self.localappdata / "Google" / "Chrome" / "User Data" / "Default" / "IndexedDB",
            self.localappdata / "Google" / "Chrome" / "User Data" / "Default" / "Cookies",
            self.localappdata / "Google" / "Chrome" / "User Data" / "Default" / "Web Data",

            # Edge
            self.localappdata / "Microsoft" / "Edge" / "User Data" / "Default" / "Local Storage",
            self.localappdata / "Microsoft" / "Edge" / "User Data" / "Default" / "Session Storage",
            self.localappdata / "Microsoft" / "Edge" / "User Data" / "Default" / "IndexedDB",
            self.localappdata / "Microsoft" / "Edge" / "User Data" / "Default" / "Cookies",
            self.localappdata / "Microsoft" / "Edge" / "User Data" / "Default" / "Web Data",

            # Firefox (more complex structure, but we'll check common locations)
            self.appdata / "Mozilla" / "Firefox" / "Profiles",
        ]

        for browser_path in browser_paths:
            if browser_path.exists():
                try:
                    for item in browser_path.iterdir():
                        if any(pattern.lower() in item.name.lower() for pattern in self.augment_patterns):
                            self.clean_directory(item, "(browser storage)")
                except:
                    pass  # Skip if we can't access browser data

    def clean_network_fingerprints(self):
        """Clean network-based fingerprinting data"""
        try:
            # Clear DNS cache to remove domain resolution history
            if self.system == "Windows":
                subprocess.run(['ipconfig', '/flushdns'], capture_output=True, shell=True)
                self.log("Flushed DNS cache", "success")
            elif self.system == "Darwin":  # macOS
                subprocess.run(['sudo', 'dscacheutil', '-flushcache'], capture_output=True)
                self.log("Flushed DNS cache", "success")
            elif self.system == "Linux":
                subprocess.run(['sudo', 'systemctl', 'restart', 'systemd-resolved'], capture_output=True)
                self.log("Restarted DNS resolver", "success")

            # Clear network adapter cache files
            network_cache_paths = [
                self.localappdata / "Microsoft" / "Windows" / "INetCache",
                self.appdata / "Microsoft" / "Windows" / "Cookies",
                Path("C:/Windows/System32/drivers/etc/hosts.bak") if self.system == "Windows" else None,
            ]

            for cache_path in network_cache_paths:
                if cache_path and cache_path.exists():
                    try:
                        for item in cache_path.iterdir():
                            if any(pattern.lower() in item.name.lower() for pattern in self.augment_patterns):
                                self.clean_directory(item, "(network cache)")
                    except:
                        pass

        except Exception as e:
            self.log(f"Network fingerprint cleaning limited: {e}", "warning")

    def clean_system_logs(self):
        """Clean Augment entries from system logs and crash reports"""
        log_paths = [
            # Windows Event Logs (we'll just log this, actual cleaning needs admin rights)
            self.localappdata / "CrashDumps",
            self.user_home / "AppData" / "Local" / "Microsoft" / "Windows" / "WER",

            # Application logs
            self.appdata / "Code" / "logs",
            self.appdata / "Code - Insiders" / "logs",
        ]

        for log_path in log_paths:
            if log_path.exists():
                try:
                    for item in log_path.iterdir():
                        if any(pattern.lower() in item.name.lower() for pattern in self.augment_patterns):
                            self.clean_directory(item, "(log file)")
                except:
                    pass

    def clean_keybindings_and_config(self):
        """Clean Augment entries from keybindings and other config files"""
        config_files = [
            # Keybindings
            self.appdata / "Code" / "User" / "keybindings.json",
            self.appdata / "Code - Insiders" / "User" / "keybindings.json",

            # Snippets
            self.appdata / "Code" / "User" / "snippets",
            self.appdata / "Code - Insiders" / "User" / "snippets",

            # Tasks and launch configurations
            self.appdata / "Code" / "User" / "tasks.json",
            self.appdata / "Code - Insiders" / "User" / "tasks.json",
        ]

        for config_path in config_files:
            if config_path.exists():
                if config_path.suffix == '.json':
                    self.clean_json_settings(config_path)
                elif config_path.is_dir():
                    # Clean snippets directory
                    try:
                        for item in config_path.iterdir():
                            if any(pattern.lower() in item.name.lower() for pattern in self.augment_patterns):
                                self.clean_directory(item, "(Augment config)")
                    except:
                        pass

    def clean_extension_host_logs(self):
        """Clean extension host logs that might contain Augment data"""
        log_paths = [
            self.appdata / "Code" / "logs",
            self.appdata / "Code - Insiders" / "logs",
            self.localappdata / "Programs" / "Microsoft VS Code" / "logs",
        ]

        for log_path in log_paths:
            if log_path.exists():
                try:
                    for item in log_path.rglob("*"):
                        if item.is_file() and any(pattern.lower() in item.name.lower() for pattern in self.augment_patterns):
                            self.clean_directory(item, "(extension log)")
                        # Also check file contents for Augment references in recent logs
                        elif item.is_file() and item.suffix == '.log':
                            try:
                                with open(item, 'r', encoding='utf-8', errors='ignore') as f:
                                    content = f.read()
                                    if any(pattern in content for pattern in ["augment", "Augment"]):
                                        self.clean_directory(item, "(log with Augment references)")
                            except:
                                pass
                except:
                    pass

    def clean_storage_json(self):
        """Clean Augment-related data from all VSCode variant storage.json files"""
        storage_files = []

        # Get storage.json files for all variants
        for variant in self.get_vscode_variants():
            variant_base = self.appdata / variant
            storage_files.extend([
                variant_base / "storage.json",
                variant_base / "User" / "globalStorage" / "storage.json"
            ])

        for storage_file in storage_files:
            if storage_file.exists():
                try:
                    with open(storage_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # Remove Augment-related keys from storage.json
                    keys_removed = []

                    # Check for telemetry keys that might be used by Augment
                    for key in list(data.keys()):
                        # Remove Augment-specific keys
                        if any(pattern.lower() in key.lower() for pattern in self.augment_patterns):
                            del data[key]
                            keys_removed.append(key)
                        # Remove specific telemetry identifiers that Augment might track
                        elif key in self.telemetry_keys:
                            del data[key]
                            keys_removed.append(key)

                    if keys_removed:
                        # Write back the cleaned storage.json
                        with open(storage_file, 'w', encoding='utf-8') as f:
                            json.dump(data, f, indent=2)

                        self.log(f"Cleaned {len(keys_removed)} keys from {storage_file.name}")
                        for key in keys_removed:
                            self.log(f"  - Removed: {key}")
                    else:
                        self.log(f"No Augment data found in {storage_file.name}")

                except Exception as e:
                    self.log(f"Error cleaning storage.json {storage_file}: {e}")

    def clean_machine_specific_data(self):
        """Clean machine-specific data that Augment might use for identification"""
        # Clean machine-specific files that might contain Augment tracking data
        machine_files = [
            self.appdata / "Code" / "machineid",
            self.appdata / "Code - Insiders" / "machineid",
            self.appdata / "Code" / "User" / "globalStorage" / "storage.json",
            self.appdata / "Code - Insiders" / "User" / "globalStorage" / "storage.json",
        ]

        for machine_file in machine_files:
            if machine_file.exists():
                try:
                    if machine_file.suffix == '.json':
                        # For JSON files, clean selectively
                        with open(machine_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        keys_removed = []
                        for key in list(data.keys()):
                            if any(pattern.lower() in key.lower() for pattern in self.augment_patterns):
                                del data[key]
                                keys_removed.append(key)

                        if keys_removed:
                            with open(machine_file, 'w', encoding='utf-8') as f:
                                json.dump(data, f, indent=2)
                            self.log(f"Cleaned {len(keys_removed)} keys from {machine_file.name}")
                    else:
                        # For non-JSON files (like machineid), check if they contain Augment data
                        with open(machine_file, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()

                        if any(pattern in content for pattern in ["augment", "Augment"]):
                            self.clean_directory(machine_file, "(machine ID with Augment data)")

                except Exception as e:
                    self.log(f"Error cleaning machine file {machine_file}: {e}")

    def clean_advanced_tracking_data(self):
        """Clean advanced tracking mechanisms used by modern AI extensions"""
        try:
            # Generate new fake identifiers
            new_machine_id, new_device_id, new_hw_sig = self.spoof_system_identifiers()

            # Advanced tracking files to clean/replace
            advanced_tracking_files = [
                # Hardware fingerprinting files
                self.localappdata / "hardware_signature.dat",
                self.localappdata / "device_fingerprint.json",
                self.appdata / "system_profile.dat",

                # Network fingerprinting
                self.localappdata / "network_signature.dat",
                self.appdata / "connection_profile.json",

                # Behavioral tracking
                self.appdata / "usage_patterns.dat",
                self.localappdata / "behavior_profile.json",

                # Codebase fingerprinting
                self.appdata / "codebase_signatures.dat",
                self.localappdata / "workspace_fingerprints.json",
            ]

            for tracking_file in advanced_tracking_files:
                if tracking_file.exists():
                    self.clean_directory(tracking_file, "(advanced tracking data)")

            # Replace VSCode machine IDs with new fake ones
            vscode_machine_files = [
                self.appdata / "Code" / "machineid",
                self.appdata / "Code - Insiders" / "machineid",
                self.appdata / "VSCodium" / "machineid",
                self.appdata / "Cursor" / "machineid",
            ]

            for machine_file in vscode_machine_files:
                if machine_file.exists():
                    try:
                        with open(machine_file, 'w', encoding='utf-8') as f:
                            f.write(new_machine_id)
                        self.log(f"Replaced machine ID in {machine_file.name}", "success")
                    except Exception as e:
                        self.log(f"Error replacing machine ID: {e}", "error")

        except Exception as e:
            self.log(f"Advanced tracking cleanup error: {e}", "error")

    def run_cleanup(self):
        """Main cleanup routine - comprehensive cross-platform Augment-only data removal"""
        self.log("🚀 Starting Enhanced Cross-Platform Augment Data Cleaner", "success")
        self.log(f"Platform: {self.system}", "info")
        self.log("WARNING: This will remove ONLY Augment-related data, all VSCode variants will remain intact!", "warning")

        # Detect VSCode installations
        active_installations = self.find_active_vscode_installations()
        if active_installations:
            self.log(f"Found {len(active_installations)} VSCode installations", "success")
        else:
            self.log("No VSCode installations detected", "warning")

        self.log(f"Scanning for {len(self.augment_patterns)} different Augment patterns...", "info")
        self.log(f"Checking {len(self.telemetry_keys)} telemetry keys...", "info")

        # Check if VSCode is running
        if self.is_vscode_running():
            self.log("VSCode processes detected. Attempting to close them...", "warning")
            self.kill_vscode_processes()
            time.sleep(2)  # Wait for processes to terminate

        # Perform comprehensive cleanup steps - only Augment-related
        self.log("1/14 Cleaning Augment extensions...")
        self.clean_augment_from_extensions()

        self.log("2/14 Cleaning VSCode storage.json (telemetry IDs)...")
        self.clean_storage_json()

        self.log("3/14 Cleaning machine-specific identification data...")
        self.clean_machine_specific_data()

        self.log("4/14 🔥 ADVANCED: Cleaning advanced tracking mechanisms...")
        self.clean_advanced_tracking_data()

        self.log("5/14 🔥 ADVANCED: Cleaning network fingerprints...")
        self.clean_network_fingerprints()

        self.log("6/14 Cleaning Augment-related VSCode settings...")
        self.clean_vscode_settings()

        self.log("7/14 Cleaning workspace-specific Augment settings...")
        self.clean_workspace_settings()

        self.log("8/14 Cleaning Augment keybindings and config...")
        self.clean_keybindings_and_config()

        self.log("9/14 Cleaning Augment extension logs...")
        self.clean_extension_host_logs()

        self.log("10/14 Cleaning Augment temporary files...")
        self.clean_temp_files()

        self.log("11/14 Cleaning Augment browser storage...")
        self.clean_browser_data()

        self.log("12/14 Cleaning Augment system logs...")
        self.clean_system_logs()

        self.log("13/14 Cleaning Augment registry entries...")
        self.clean_registry_entries()

        self.log("14/14 Cleaning Augment environment variables...")
        self.clean_environment_variables()

        self.log("=" * 70)
        self.log("🎯 ULTRA-ENHANCED AUGMENT CLEANUP COMPLETED! (v0.515.0+ Compatible)")
        self.log("=" * 70)
        self.log("VSCode settings and other extensions preserved.")
        self.log("")
        self.log("✅ All Augment-related data has been removed from:")
        self.log("  ✓ Extensions and storage")
        self.log("  ✓ VSCode storage.json (telemetry IDs)")
        self.log("  ✓ Machine identification data")
        self.log("  🔥 ADVANCED: Hardware fingerprinting data")
        self.log("  🔥 ADVANCED: Network fingerprints & DNS cache")
        self.log("  🔥 ADVANCED: Behavioral tracking patterns")
        self.log("  🔥 ADVANCED: Codebase fingerprinting")
        self.log("  ✓ Settings and configuration")
        self.log("  ✓ Keybindings and snippets")
        self.log("  ✓ Extension host logs")
        self.log("  ✓ Temporary and cache files")
        self.log("  ✓ Enhanced browser storage (cookies, web data)")
        self.log("  ✓ System logs")
        self.log("  ✓ Registry entries")
        self.log("  ✓ Environment variables")
        self.log("")
        self.log("🔍 Ultra-Enhanced telemetry detection:")
        self.log(f"  - {len(self.telemetry_keys)} known telemetry/ID patterns")
        self.log(f"  - {len(self.storage_patterns)} storage patterns")
        self.log(f"  - {len(self.augment_patterns)} general Augment patterns")
        self.log("")
        self.log("🚀 NEW v0.515.0+ Anti-Detection Features:")
        self.log("  ✓ Hardware signature spoofing")
        self.log("  ✓ System fingerprint randomization")
        self.log("  ✓ Network identity masking")
        self.log("  ✓ Behavioral pattern disruption")
        self.log("  ✓ Advanced trial tracking bypass")
        self.log("  ✓ Professional trial reset capability")
        self.log("  ✓ 7-day trial tracking elimination")
        self.log("")
        self.log("🎉 MAXIMUM STEALTH MODE ACTIVATED!")
        self.log("   All tracking identifiers have been completely randomized.")
        self.log("   Trial period should be fully reset and undetectable.")

def main():
    """Main entry point"""
    print("=" * 80)
    print("🎯 ULTRA-ENHANCED CROSS-PLATFORM AUGMENT TRAIL BYPASS TOOL")
    print("Advanced stealth tool for complete Augment tracking elimination (v0.515.0+)")
    print("=" * 80)

    # Detect platform
    system = platform.system()
    print(f"\n🖥️  Platform: {system}")

    print("\n🔧 This ULTRA-ENHANCED tool will:")
    print("✅ Support all VSCode variants (Code, Insiders, VSCodium, Cursor)")
    print("✅ Work cross-platform (Windows, macOS, Linux)")
    print("✅ Remove Augment extensions from all variants")
    print("✅ Clean telemetry IDs and machine fingerprints")
    print("✅ Remove Augment-related settings and storage")
    print("✅ Clean temporary files and browser data")
    print("🔥 SPOOF hardware signatures and system identifiers")
    print("🔥 RANDOMIZE machine IDs and device fingerprints")
    print("🔥 ELIMINATE network and behavioral tracking")
    print("🔥 BYPASS advanced trial period detection")
    print("❌ Preserve all other VSCode data and settings")

    print("\n🎨 STEALTH Features (v0.515.0+ Compatible):")
    print("• Hardware signature spoofing")
    print("• System fingerprint randomization")
    print("• Network identity masking")
    print("• DNS cache clearing")
    print("• Behavioral pattern disruption")
    print("• Professional trial reset")
    print("• 7-day trial tracking bypass")
    print("• Advanced anti-detection mechanisms")

    # Warning prompt
    response = input(f"\n⚠️  This will remove ONLY Augment-related data on {system}. Continue? (y/N): ")
    if response.lower() != 'y':
        print("❌ Operation cancelled.")
        return

    try:
        cleaner = AugmentCleaner()
        cleaner.run_cleanup()
    except Exception as e:
        error_color(f"Unexpected error: {e}")
        input("\nPress Enter to exit...")
        return

    input("\n✨ Press Enter to exit...")

if __name__ == "__main__":
    main()
